import { useAtom } from 'jotai';
import { useCallback, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

import { itemsAtom, selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { IItem, ITEM_STATUS } from '../../../types/item.type';

export const useCanvasClipboard = () => {
	const [items, setItems] = useAtom(itemsAtom);
	const [clipboard, setClipboard] = useState<IItem[] | null>(null);
	const [selectedItemsIds, setItemsSelectedIds] = useAtom(selectedItemsIdsAtom);

	const handleCopy = useCallback(() => {
		const itemsToCopy = items.filter((item) => selectedItemsIds.includes(item.tempId));
		if (itemsToCopy.length > 0) {
			setClipboard(itemsToCopy.map((item) => ({ ...item })));
		}
	}, [items, selectedItemsIds]);

	const handlePaste = useCallback(() => {
		if (clipboard) {
			const maxLayer = items.reduce((max, item) => Math.max(max, item.layer), 0);
			const newItems = clipboard.map((item) => ({
				...item,
				tempId: uuidv4(),
				layer: maxLayer + item.layer + 1,
				status: ITEM_STATUS.NEW,
				position: {
					x: item.position.x + 10,
					y: item.position.y + 10,
				},
			}));
			setItems([...items, ...newItems]);
			setItemsSelectedIds(newItems.map((item) => item.tempId));
		}
	}, [clipboard, items, setItems, setItemsSelectedIds]);

	const handleDelete = useCallback(() => {
		if (selectedItemsIds.length > 0) {
			setItems(items.map((item) => (selectedItemsIds.includes(item.tempId) ? { ...item, status: ITEM_STATUS.DELETED } : item)));
			setItemsSelectedIds([]);
		}
	}, [items, selectedItemsIds, setItems, setItemsSelectedIds]);

	return {
		handleCopy,
		handlePaste,
		handleDelete,
		hasClipboardContent: clipboard !== null && clipboard.length > 0,
	};
};
