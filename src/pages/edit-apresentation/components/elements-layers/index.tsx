import { motion, Reorder } from 'framer-motion';
import { useAtom } from 'jotai';
import { Layers, ClipboardPaste } from 'lucide-react';
import { useCallback, useMemo } from 'react';
import { toast } from 'sonner';
import { selectedItemsIdsAtom } from '../../../../shared/states/items/object-item.state';
import { useCanvasClipboard } from '../../hooks/canvas/keyboards/canvas-clipboard.hook';
import { useContextMenu } from '../../hooks/cursor/context-menu.hook';
import { useItemSelection } from '../../hooks/element/item-selection.hook';
import { useLayerManagement } from '../../hooks/element/layer-management.hook';
import { ElementsLayerItem } from './element-layer-item';

const containerVariants = {
	hidden: { opacity: 0 },
	visible: {
		opacity: 1,
		transition: {
			staggerChildren: 0.1,
		},
	},
};

const itemVariants = {
	hidden: { y: 20, opacity: 0 },
	visible: {
		y: 0,
		opacity: 1,
		transition: {
			type: 'spring',
			stiffness: 300,
			damping: 24,
		},
	},
};

export const ElementsLayer = () => {
	const { handleReorder, layeredItems } = useLayerManagement();
	const [selectedIds] = useAtom(selectedItemsIdsAtom);
	const { handleSelect } = useItemSelection(layeredItems);
	const { handlePaste, hasClipboardContent } = useCanvasClipboard();
	const { openContextMenu } = useContextMenu();

	const contextMenuOptions = useMemo(
		() => [
			{
				label: 'Colar',
				onClick: () => {
					handlePaste();
					toast.success('Item colado!');
				},
				icon: <ClipboardPaste className="mr-2" size={14} />,
				disabled: !hasClipboardContent,
			},
		],
		[handlePaste, hasClipboardContent],
	);

	const handleRightClick = useCallback(
		(e: React.MouseEvent) => {
			e.preventDefault();
			openContextMenu(e.clientX, e.clientY, contextMenuOptions);
		},
		[openContextMenu, contextMenuOptions],
	);

	return (
		<div className="h-full p-2" data-selection-enabled="true" onContextMenu={handleRightClick}>
			{layeredItems.length > 0 ? (
				<Reorder.Group
					axis="y"
					values={layeredItems}
					onReorder={handleReorder}
					layoutScroll
					className="flex flex-col gap-1"
					data-selection-enabled="true"
				>
					{layeredItems.map((item) => (
						<Reorder.Item
							key={item.tempId}
							value={item}
							dragMomentum={false}
							dragElastic={0}
							dragTransition={{ bounceStiffness: 600, bounceDamping: 20 }}
							whileDrag={{
								backgroundColor: 'rgba(90, 90, 90, 0.2)',
							}}
						>
							<ElementsLayerItem item={item} isSelected={selectedIds.includes(item.tempId)} onSelect={handleSelect} />
						</Reorder.Item>
					))}
				</Reorder.Group>
			) : (
				<motion.div
					className="flex h-full flex-col items-center justify-center p-4 text-center"
					variants={containerVariants}
					initial="hidden"
					animate="visible"
				>
					<motion.div className="mb-3 flex h-10 w-10 items-center justify-center opacity-60" variants={itemVariants}>
						<Layers className="text-[#8c8c8c]" size={20} />
					</motion.div>
					<motion.h3 className="mb-1 text-xs font-medium text-[#8c8c8c]" variants={itemVariants}>
						Nenhuma camada
					</motion.h3>
					<motion.p className="text-xs text-[#6c6c6c]" variants={itemVariants}>
						Adicione elementos ao seu projeto
					</motion.p>
				</motion.div>
			)}
		</div>
	);
};
