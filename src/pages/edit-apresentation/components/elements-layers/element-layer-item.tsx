import { Toolt<PERSON>, TooltipContent, <PERSON><PERSON><PERSON><PERSON><PERSON>ider, TooltipTrigger } from '@/components/shadcnui/tooltip';
import { motion } from 'framer-motion';
import { Calendar, Clock, CloudSunRain, Copy, Grid, Image, Layers, Map, RectangleHorizontal, Trash2, TypeIcon } from 'lucide-react';
import { useCallback, useMemo } from 'react';
import { toast } from 'sonner';
import { useCanvasClipboard } from '../../hooks/canvas/keyboards/canvas-clipboard.hook';
import { useContextMenu } from '../../hooks/cursor/context-menu.hook';
import { ElementsLayerItemProps } from '../../types/elements/layer.type';
import { ITEM_SHAPE_TYPE } from '../../types/item.type';
import { StatusIcon } from './status-icon';
import { useSetAtom } from 'jotai';
import { selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';

const ICON_MAPPING: Record<string, JSX.Element> = {
	[ITEM_SHAPE_TYPE.RECTANGLE]: <RectangleHorizontal size={16} className="text-[#4B9FFF]" />,
	[ITEM_SHAPE_TYPE.CLOCK]: <Clock size={16} className="text-[#F5A623]" />,
	[ITEM_SHAPE_TYPE.CALENDAR]: <Calendar size={16} className="text-[#66BF3C]" />,
	[ITEM_SHAPE_TYPE.CAROUSEL]: <Grid size={16} className="text-[#9B51E0]" />,
	[ITEM_SHAPE_TYPE.IMAGE]: <Image size={16} className="text-[#FF7262]" />,
	[ITEM_SHAPE_TYPE.TEXT]: <TypeIcon size={16} className="text-[#2ECFCA]" />,
	[ITEM_SHAPE_TYPE.WEATHER]: <CloudSunRain size={16} className="text-[#FFD43B]" />,
	[ITEM_SHAPE_TYPE.MAP]: <Map size={16} className="text-[#FF7262]" />,
};

function useElementsLayerItem(item: ElementsLayerItemProps['item'], onSelect: ElementsLayerItemProps['onSelect']) {
	const { openContextMenu } = useContextMenu();
	const { handleCopy, handleDelete } = useCanvasClipboard();
	const setSelectItem = useSetAtom(selectedItemsIdsAtom);

	const contextMenuOptions = useMemo(
		() => [
			{
				label: 'Copiar',
				onClick: () => {
					setSelectItem([item.tempId]);
					handleCopy();
				},
				icon: <Copy className="mr-2" size={14} />,
			},
			{
				label: 'Excluir',
				onClick: () => {
					setSelectItem([item.tempId]);
					handleDelete();
				},
				icon: <Trash2 className="mr-2" size={14} />,
			},
		],
		[handleCopy, handleDelete, setSelectItem, item.tempId],
	);

	const handleClick = useCallback(
		(e: React.MouseEvent<HTMLDivElement>) => {
			onSelect({
				itemId: item.tempId,
				modifiers: {
					shiftKey: e.shiftKey,
					ctrlKey: e.ctrlKey || e.metaKey,
				},
			});
		},
		[item.tempId, onSelect],
	);

	const handleRightClick = useCallback(
		(e: React.MouseEvent) => {
			e.preventDefault();
			openContextMenu(e.clientX, e.clientY, contextMenuOptions);
		},
		[openContextMenu, contextMenuOptions],
	);

	return {
		handleClick,
		handleRightClick,
		getIcon: () => ICON_MAPPING[item.type] || <Layers size={16} className="text-[#8c8c8c]" />,
	};
}

const TooltipDetails: React.FC<{ item: ElementsLayerItemProps['item'] }> = ({ item }) => (
	<div className="flex flex-col gap-1 text-xs">
		<p className="font-semibold">{item.name}</p>
		<p className="text-[#8c8c8c]">Tipo: {item.type}</p>
		<p className="text-[#8c8c8c]">Camada: {item.layer}</p>
		<p className="text-[#8c8c8c]">
			Tamanho: {item.size.width}px x {item.size.height}px
		</p>
		<p className="text-[#8c8c8c]">
			Posição: {item.position.x.toFixed(2)} x {item.position.y.toFixed(2)}
		</p>
	</div>
);

export const ElementsLayerItem: React.FC<ElementsLayerItemProps> = ({ item, isSelected, onSelect }) => {
	const { handleClick, handleRightClick, getIcon } = useElementsLayerItem(item, onSelect);

	return (
		<TooltipProvider>
			<Tooltip>
				<TooltipTrigger asChild>
					<motion.div
						onClick={handleClick}
						onContextMenu={handleRightClick}
						className={`elements-layer-item group flex h-10 w-full cursor-pointer select-none items-center gap-3 rounded-md border bg-[#2d2d2d] px-3 transition-colors duration-150 ${
							isSelected ? 'border-primary shadow-[0_2px_8px_0_rgba(140,140,140,0.10)]' : 'border-[#333333] hover:bg-[#2d2d2d]'
						}`}
						whileTap={{ scale: 0.98, backgroundColor: '#222' }}
						tabIndex={0}
						aria-selected={isSelected}
						data-selection-enabled="true"
					>
						<div className="flex h-7 w-7 items-center justify-center rounded">{getIcon()}</div>
						<div className="flex-1 truncate">
							<span className={`text-sm font-medium ${isSelected ? 'text-[#e0e0e0]' : 'text-[#b0b0b0]'}`}>{item.name}</span>
						</div>
						<StatusIcon currentItemActive={isSelected} status={item.status} />
					</motion.div>
				</TooltipTrigger>
				<TooltipContent side="right" className="rounded-md border-[#333333] bg-[#252525] text-[#e0e0e0] shadow-lg">
					<TooltipDetails item={item} />
				</TooltipContent>
			</Tooltip>
		</TooltipProvider>
	);
};
