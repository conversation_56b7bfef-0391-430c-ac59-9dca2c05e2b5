import { useAtomValue } from 'jotai';
import { useEffect, useState } from 'react';
import { selectedItemsIdsAtom } from '@/shared/states/items/object-item.state';
import { useCanvasClipboard } from '../../hooks/canvas/keyboards/canvas-clipboard.hook';
import { useCanvasLayers } from '../../hooks/canvas/keyboards/canvas-layers.hook';

interface KeyboardAction {
	action: string;
	timestamp: number;
}

export const KeyboardDebug: React.FC = () => {
	const [actions, setActions] = useState<KeyboardAction[]>([]);
	const [isVisible, setIsVisible] = useState(false);
	const selectedItemsIds = useAtomValue(selectedItemsIdsAtom);
	const { hasClipboardContent } = useCanvasClipboard();

	const addAction = (action: string) => {
		setActions(prev => [
			{ action, timestamp: Date.now() },
			...prev.slice(0, 4) // Mantém apenas as últimas 5 ações
		]);
	};

	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			if (e.ctrlKey || e.metaKey) {
				switch (e.key.toLowerCase()) {
					case 'c':
						addAction('Copy (Ctrl+C)');
						break;
					case 'v':
						addAction('Paste (Ctrl+V)');
						break;
					case 'd':
						addAction('Duplicate (Ctrl+D)');
						break;
				}
			}
			if (e.key === 'Delete') {
				addAction('Delete (Del)');
			}
		};

		document.addEventListener('keydown', handleKeyDown);
		return () => document.removeEventListener('keydown', handleKeyDown);
	}, []);

	// Auto-hide após 3 segundos
	useEffect(() => {
		if (actions.length > 0) {
			setIsVisible(true);
			const timer = setTimeout(() => setIsVisible(false), 3000);
			return () => clearTimeout(timer);
		}
	}, [actions]);

	if (!isVisible || actions.length === 0) return null;

	return (
		<div className="fixed top-4 right-4 z-50 bg-black/80 text-white p-3 rounded-lg shadow-lg max-w-xs">
			<div className="text-xs font-semibold mb-2">Atalhos de Teclado</div>
			<div className="text-xs space-y-1">
				<div>Selecionados: {selectedItemsIds.length}</div>
				<div>Clipboard: {hasClipboardContent ? 'Sim' : 'Não'}</div>
				<div className="border-t border-gray-600 pt-1 mt-2">
					{actions.map((action, index) => (
						<div key={action.timestamp} className={`opacity-${100 - index * 20}`}>
							{action.action}
						</div>
					))}
				</div>
			</div>
		</div>
	);
};
